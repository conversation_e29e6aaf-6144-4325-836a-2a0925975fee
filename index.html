<!DOCTYPE html>
<html lang="cs">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Adresář OTE">
    <meta name="author" content="<PERSON>">
    <link rel="shortcut icon" href="img/favicon.ico" type="image/x-icon">
    <title>Adresář OTE</title>
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@200;300;400;600;700;800;900&display=swap"
        rel="stylesheet">
    <script src="https://kit.fontawesome.com/14940b9e28.js" crossorigin="anonymous"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/directory.css">
    <link rel="stylesheet" href="css/map-integration.css">
    <style>

        .navigation-buttons {
            display: flex;
            gap: 0.5rem;
            margin: 0 1rem;
        }

        .nav-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.25rem;
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(10px);
            text-decoration: none;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            color: #fff;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .nav-btn.active {
            background: rgba(255, 255, 255, 0.95);
            border-color: rgba(255, 255, 255, 0.8);
            color: #2563eb;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .nav-btn i {
            font-size: 1.1rem;
        }

        @media (max-width: 768px) {
            .navigation-buttons {
                margin: 0 0.5rem;
                gap: 0.25rem;
            }

            .nav-btn {
                padding: 0.5rem 0.75rem;
                font-size: 0.8rem;
            }

            .nav-btn span {
                display: none;
            }

            .nav-btn i {
                font-size: 1.2rem;
            }
        }


        .office_map_section {
            padding: 2rem 0;
            background: #f7f9fb;
            min-height: calc(100vh - 200px);
        }


        .section-transition {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .section-transition.active {
            opacity: 1;
            transform: translateY(0);
        }


        body {
            font-family: 'Nunito', Arial, sans-serif;
        }


        .employee-directory-container {
            display: block;
            position: relative;
        }

        .departments-panel-directory,
        .departments-panel {
            position: fixed;
            left: 0;
            top: 80px;
            width: 280px;
            height: calc(100vh - 80px);
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-right: 2px solid #e5e7eb;
            box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
            z-index: 20;
            overflow-y: auto;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            transform: translateX(0);
        }

        .departments-panel-directory.collapsed,
        .departments-panel.collapsed {
            transform: translateX(-100%);
            width: 280px;
        }

        .panel-toggle-btn {
            position: fixed;
            left: 280px;
            top: 50%;
            transform: translateY(-50%);
            width: 40px;
            height: 60px;
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            border: none;
            border-radius: 0 12px 12px 0;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 25;
        }

        .panel-toggle-btn:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            box-shadow: 4px 0 15px rgba(0, 0, 0, 0.2);
            transform: translateY(-50%) translateX(2px);
        }

        .panel-toggle-btn i {
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Collapsed state styles */
        .panel-toggle-btn.collapsed {
            left: 0px;
            border-radius: 0 12px 12px 0;
        }

        .panel-toggle-btn.collapsed i {
            transform: rotate(180deg);
        }

        .departments-panel-directory .panel-header,
        .departments-panel .panel-header {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: white;
            padding: 1.5rem;
            border-bottom: 2px solid #1d4ed8;
        }

        .departments-panel-directory .panel-header h3,
        .departments-panel .panel-header h3 {
            margin: 0;
            font-size: 1.1rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .departments-list {
            padding: 1rem;
            height: calc(100vh - 160px);
            overflow-y: auto;
        }

        .department-item {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 0.75rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .department-item:hover {
            background: #f1f5f9;
            border-color: #3b82f6;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
        }

        .department-item.active {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: white;
            border-color: #1d4ed8;
            box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
        }

        .department-item.all-employees {
            background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
            color: white;
            border-color: #374151;
        }

        .department-item.all-employees:hover {
            background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
            border-color: #6b7280;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(107, 114, 128, 0.15);
        }

        .department-item.all-employees.active {
            background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
            color: white;
            border-color: #374151;
            box-shadow: 0 4px 16px rgba(107, 114, 128, 0.3);
        }

        .department-name {
            font-weight: 600;
            font-size: 0.95rem;
            margin-bottom: 0.25rem;
        }

        .department-count {
            font-size: 0.85rem;
            opacity: 0.8;
        }

        .employee-content {
            margin-left: 280px;
            margin-top: 100px;
            padding: 0 2rem;
            min-width: 0;
            transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .employee-content.panel-collapsed {
            margin-left: 40px;
        }


        .dark-mode .departments-panel-directory,
        .dark-mode .departments-panel {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            border-right-color: #475569;
        }

        .dark-mode .departments-panel-directory .panel-header,
        .dark-mode .departments-panel .panel-header {
            background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
            border-bottom-color: #334155;
        }

        .dark-mode .department-item {
            background: #334155;
            border-color: #475569;
            color: #e2e8f0;
        }

        .dark-mode .department-item:hover {
            background: #475569;
            border-color: #3b82f6;
        }

        .dark-mode .department-item.active {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: #ffffff;
            border-color: #1d4ed8;
        }

        .dark-mode .department-item.all-employees {
            background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
            color: #e5e7eb;
            border-color: #6b7280;
        }

        .dark-mode .department-item.all-employees:hover {
            background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
            border-color: #4b5563;
        }

        .dark-mode .department-item.all-employees.active {
            background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
            color: #ffffff;
            border-color: #6b7280;
            box-shadow: 0 4px 16px rgba(75, 85, 99, 0.4);
        }

        .dark-mode .panel-toggle-btn {
            background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%);
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.3);
        }

        .dark-mode .panel-toggle-btn:hover {
            background: linear-gradient(135deg, #1d4ed8 0%, #1e3a8a 100%);
            box-shadow: 4px 0 15px rgba(0, 0, 0, 0.4);
        }


        @media (max-width: 768px) {
            .departments-panel-directory {
                width: 100%;
                height: auto;
                position: relative;
                top: 0;
                left: 0;
                transform: none !important;
            }

            .departments-panel-directory.collapsed {
                transform: none !important;
            }

            .employee-content {
                margin-left: 0;
                margin-top: 120px;
                padding: 0 1rem;
            }

            .employee-content.panel-collapsed {
                margin-left: 0;
            }

            .departments-list {
                height: auto;
                display: flex;
                flex-wrap: wrap;
                gap: 0.5rem;
                padding: 1rem;
            }

            .department-item {
                margin-bottom: 0;
                flex: 1;
                min-width: 150px;
            }

            .panel-toggle-btn {
                display: none;
            }
        }

        .main-flex {
            display: flex;
            flex-direction: row;
            align-items: flex-start;
            justify-content: flex-start;
            width: 100vw;
            max-width: none;
            margin: 2rem 0 2rem 0;
            gap: 48px;
        }


        .employee.manager {
            position: relative;

        }


        .hierarchy-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
            padding: 0.4rem 0.6rem;
            border-radius: 16px;
            font-size: 0.7rem;
            font-weight: 600;
            z-index: 5;
            box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            display: flex;
            align-items: center;
            gap: 0.3rem;
            transition: all 0.3s ease;
        }

        .hierarchy-badge.predstavenstvo {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .hierarchy-badge.vedouci {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
        }

        .hierarchy-badge i {
            font-size: 0.65rem;
        }

        .dark-mode .hierarchy-badge {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            border-color: rgba(255, 255, 255, 0.1);
        }

        .dark-mode .hierarchy-badge.predstavenstvo {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
        }

        .dark-mode .modal-badge {
            border-color: rgba(255, 255, 255, 0.1);
        }

        .dark-mode .modal-content {
            background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
            border-color: rgba(255, 255, 255, 0.1);
        }

        .dark-mode .info-card {
            background: rgba(30, 41, 59, 0.7);
            border-color: rgba(59, 130, 246, 0.3);
            color: #e2e8f0;
        }

        .dark-mode .contact-item {
            background: rgba(30, 41, 59, 0.6);
            border-color: rgba(16, 185, 129, 0.3);
            color: #e2e8f0;
        }


        .modal-content {
            background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 24px;
            box-shadow:
                0 25px 60px rgba(0,0,0,0.15),
                0 8px 25px rgba(59, 130, 246, 0.1),
                inset 0 1px 0 rgba(255,255,255,0.8);
            padding: 1.5rem;
            max-width: 420px;
            width: 85%;
            max-height: 85vh;
            overflow-y: auto;
        }

        /* Optimalizace pro Windows scale 125% a 150% */
        @media screen and (min-width: 1536px) and (max-width: 1920px) {
            /* Pravděpodobně 125% scale na 1920x1080 */
            .modal-content {
                max-width: 400px;
                padding: 1.4rem;
                border-radius: 20px;
            }

            .modal-header-section img {
                width: 110px;
                height: 110px;
            }

            .modal-content h2 {
                font-size: 1.6rem;
            }

            .info-card {
                padding: 0.8rem;
                font-size: 0.95rem;
            }

            .contact-item {
                padding: 0.7rem 0.9rem;
                font-size: 0.9rem;
            }
        }

        @media screen and (min-width: 1280px) and (max-width: 1535px) {
            /* Pravděpodobně 150% scale na 1920x1080 */
            .modal-content {
                max-width: 360px;
                padding: 1.2rem;
                border-radius: 18px;
            }

            .modal-header-section img {
                width: 100px;
                height: 100px;
            }

            .modal-content h2 {
                font-size: 1.5rem;
            }

            .info-card {
                padding: 0.7rem;
                font-size: 0.9rem;
            }

            .contact-item {
                padding: 0.6rem 0.8rem;
                font-size: 0.85rem;
            }

            .modal-badge {
                padding: 0.35rem 0.7rem;
                font-size: 0.75rem;
            }
        }

        .modal-content h2 {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
            flex-wrap: wrap;
            margin-bottom: 1.5rem;
            font-size: 1.75rem;
            color: #1e293b;
        }


        .modal-badge, .hierarchy-badge {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
            padding: 0.4rem 0.8rem;
            border-radius: 16px;
            font-size: 0.75rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.4rem;
            box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .modal-badge.predstavenstvo, .hierarchy-badge.predstavenstvo {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .modal-badge.vedouci, .hierarchy-badge.vedouci {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
        }


        .modal-header-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .modal-header-section img {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            margin-bottom: 1rem;
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
            border: 3px solid rgba(255, 255, 255, 0.9);
        }

        /* Optimalizace pro Windows scale - obecné prvky */
        @media screen and (min-width: 1536px) and (max-width: 1920px) {
            /* 125% scale optimalizace */
            .departments-panel-directory,
            .departments-panel {
                width: 270px;
            }

            .employee-content {
                margin-left: 270px;
            }

            .employee-content.panel-collapsed {
                margin-left: 38px;
            }

            .employee-bar {
                left: 270px;
                width: calc(100% - 310px);
            }

            .employee-bar.panel-collapsed {
                width: 100%;
                margin-left: 0px;
            }

            .panel-toggle-btn {
                left: 270px;
                width: 38px;
                height: 55px;
            }

            .panel-toggle-btn.collapsed {
                left: 0px;
            }

            .mapa-wrapper,
            .mapa-wrapper-custom {
                margin-left: 270px;
            }

            .mapa-wrapper.panel-collapsed,
            .mapa-wrapper-custom.panel-collapsed {
                margin-left: 0px;
            }
        }

        @media screen and (min-width: 1280px) and (max-width: 1535px) {
            /* 150% scale optimalizace */
            .departments-panel-directory,
            .departments-panel {
                width: 250px;
            }

            .employee-content {
                margin-left: 250px;
            }

            .employee-content.panel-collapsed {
                margin-left: 35px;
            }

            .employee-bar {
                left: 250px;
                width: calc(100% - 290px);
            }

            .employee-bar.panel-collapsed {
                width: 100%;
                margin-left: 0px;
            }

            .panel-toggle-btn {
                left: 250px;
                width: 35px;
                height: 50px;
                font-size: 1.1rem;
            }

            .panel-toggle-btn.collapsed {
                left: 0px;
            }

            .mapa-wrapper,
            .mapa-wrapper-custom {
                margin-left: 250px;
            }

            .mapa-wrapper.panel-collapsed,
            .mapa-wrapper-custom.panel-collapsed {
                margin-left: 0px;
            }

            .search-input {
                font-size: 0.85rem;
                padding: 0 2.25rem 0 2.25rem;
            }

            .department-item {
                padding: 0.85rem;
                font-size: 0.9rem;
            }
        }

        .modal-info-cards {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
            margin-bottom: 1.5rem;
            align-items: center;
            width: 100%;
        }

        .info-card {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.7);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 12px;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            width: 100%;
            max-width: 400px;
            text-align: center;
        }

        .info-card:hover {
            background: rgba(255, 255, 255, 0.9);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
        }

        .info-card i {
            color: #3b82f6;
            font-size: 1.1rem;
            width: 20px;
            text-align: center;
        }

        .modal-contact-section {
            margin-top: 1.5rem;
        }

        .contact-grid {
            display: grid;
            gap: 0.75rem;
            margin-bottom: 1.5rem;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 1rem;
            background: rgba(255, 255, 255, 0.6);
            border: 1px solid rgba(16, 185, 129, 0.2);
            border-radius: 10px;
            backdrop-filter: blur(5px);
            transition: all 0.3s ease;
        }

        .contact-item:hover {
            background: rgba(255, 255, 255, 0.8);
            border-color: rgba(16, 185, 129, 0.4);
        }

        .contact-item i {
            color: #10b981;
            font-size: 1rem;
            width: 18px;
            text-align: center;
        }

        .modal-actions {
            display: flex;
            gap: 1rem;
            margin-top: 1.5rem;
            flex-wrap: wrap;
            justify-content: center;
        }

        .modal-action-btn {
            position: relative;
            overflow: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .modal-action-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .modal-action-btn:hover::before {
            left: 100%;
        }

        .modal-action-btn.map-btn {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
        }

        .modal-action-btn.map-btn:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
            transform: translateY(-2px);
        }

        .main-flex-custom {
            flex-direction: column;
            gap: 16px;
            align-items: stretch;
            padding-top: 200px;
            margin-left: 280px;
            transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 1;
            position: relative;
        }

        .main-flex-custom.panel-collapsed {
            margin-left: 0px;
        }

        .employee-bar {
            width: calc(100% - 280px);
            margin: 0;
            background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
            box-shadow:
                0 8px 24px rgba(37,99,235,0.06),
                0 4px 8px rgba(37,99,235,0.04),
                inset 0 1px 0 rgba(255,255,255,0.8);
            border-radius: 20px;
            border: 1px solid rgba(37,99,235,0.1);
            padding: 1rem 1.5rem;
            position: fixed;
            top: 80px;
            left: 280px;
            z-index: 10;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            height: auto;
            min-height: 100px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .employee-bar.panel-collapsed {
            width: 100%;
            left: 0px;
        }

        /* Zjednodušené styly pro vyhledávací lištu */
        .search-container-simple {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(59, 130, 246, 0.15);
            border-radius: 16px;
            margin: 1.5rem 0;
            padding: 1.5rem;
            box-shadow: 0 4px 20px rgba(59, 130, 246, 0.1);
            transition: all 0.3s ease;
        }

        .dark-mode .search-container-simple {
            background: rgba(30, 41, 59, 0.95);
            border-color: rgba(59, 130, 246, 0.3);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .search-input-wrapper {
            position: relative;
            margin-bottom: 1rem;
        }

        .search-input-simple {
            width: 100%;
            padding: 1rem 3rem 1rem 3rem;
            border: 2px solid rgba(59, 130, 246, 0.2);
            border-radius: 12px;
            font-size: 1rem;
            background: rgba(255, 255, 255, 0.9);
            transition: all 0.3s ease;
            outline: none;
            color: #1f2937;
            font-weight: 500;
        }

        .search-input-simple:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            background: rgba(255, 255, 255, 1);
        }

        .dark-mode .search-input-simple {
            background: rgba(30, 41, 59, 0.9);
            border-color: rgba(59, 130, 246, 0.3);
            color: #e2e8f0;
        }

        .dark-mode .search-input-simple:focus {
            background: rgba(30, 41, 59, 1);
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
        }

        .map-search-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(59, 130, 246, 0.15);
            border-radius: 16px;
            margin: 1rem;
            padding: 1.5rem;
            box-shadow: 0 4px 20px rgba(59, 130, 246, 0.1);
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .dark-mode .map-search-header {
            background: rgba(30, 41, 59, 0.95);
            border-color: rgba(59, 130, 246, 0.3);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }



        .department-title {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 16px;
            font-weight: 600;
            font-size: 1rem;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
            min-width: 200px;
            justify-content: center;
            white-space: nowrap;
            flex-shrink: 0;
            transition: all 0.3s ease;
        }

        .dark-mode .department-title {
            background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%);
            box-shadow: 0 4px 12px rgba(30, 64, 175, 0.4);
        }


        .unified-search-container {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 255, 0.95) 100%);
            backdrop-filter: blur(25px);
            border: 2px solid rgba(59, 130, 246, 0.12);
            border-radius: 16px;
            padding: 1rem 1.25rem;
            margin-bottom: 1.5rem;
            box-shadow:
                0 8px 32px rgba(59, 130, 246, 0.08),
                0 2px 8px rgba(0, 0, 0, 0.04),
                inset 0 1px 0 rgba(255, 255, 255, 0.9);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .unified-search-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.03), transparent);
            transition: left 0.6s ease;
        }

        .unified-search-container:hover {
            box-shadow:
                0 12px 48px rgba(59, 130, 246, 0.15),
                0 4px 16px rgba(0, 0, 0, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 0.9);
            transform: translateY(-3px);
            border-color: rgba(59, 130, 246, 0.2);
        }

        .unified-search-container:hover::before {
            left: 100%;
        }


        .search-row-inline {
            display: flex;
            align-items: stretch;
            justify-content: flex-start;
            gap: 1rem;
            width: 100%;
            margin: 0;
            height: 40px;
        }

        .section-indicator-inline {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.6rem;
            padding: 0 1rem;
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: white;
            border-radius: 12px;
            font-weight: 700;
            font-size: 0.9rem;
            box-shadow:
                0 4px 12px rgba(59, 130, 246, 0.3),
                0 2px 4px rgba(59, 130, 246, 0.2);
            flex-shrink: 0;
            width: 220px;
            height: 44px;
            box-sizing: border-box;
            line-height: 1;
            margin: 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .section-indicator-inline::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .section-indicator-inline:hover {
            transform: translateX(2px) scale(1.02);
            box-shadow:
                0 6px 16px rgba(59, 130, 246, 0.4),
                0 2px 8px rgba(59, 130, 246, 0.3);
        }

        .section-indicator-inline:hover::before {
            left: 100%;
        }

        .section-indicator-inline i {
            opacity: 0.95;
            font-size: 0.9rem;
            filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
            transition: all 0.3s ease;
        }

        .section-indicator-inline:hover i {
            opacity: 1;
            transform: scale(1.1);
        }

        .section-indicator-inline span {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .search-window-inline {
            flex: 1;
            position: relative;
            min-width: 0;
            height: 44px;
            box-sizing: border-box;
        }

        .search-input-wrapper {
            position: relative;
            width: 100%;
            height: 100%;
            box-sizing: border-box;
        }

        .search-input {
            width: 100%;
            height: 100%;
            padding: 0 3rem 0 2.8rem;
            border: 2px solid rgba(59, 130, 246, 0.15);
            border-radius: 14px;
            font-size: 0.9rem;
            font-weight: 500;
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            outline: none;
            color: #1f2937;
            box-sizing: border-box;
            line-height: 1;
            margin: 0;
            box-shadow:
                0 4px 12px rgba(59, 130, 246, 0.06),
                inset 0 1px 0 rgba(255, 255, 255, 0.9);
        }

        .search-input:focus {
            border-color: #3b82f6;
            background: #ffffff;
            box-shadow:
                0 0 0 3px rgba(59, 130, 246, 0.12),
                0 8px 24px rgba(59, 130, 246, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.9);
            transform: translateY(-1px);
        }

        .search-input::placeholder {
            color: #6b7280;
            font-weight: 400;
            transition: color 0.3s ease;
        }

        .search-input:focus::placeholder {
            color: #9ca3af;
        }


        #directorySearchInput {
            height: 100% !important;
            padding: 0 2.5rem 0 2.5rem !important;
            line-height: 1 !important;
            box-sizing: border-box !important;
            margin: 0 !important;
            border: 1px solid rgba(59, 130, 246, 0.2) !important;
            border-radius: 10px !important;
            font-size: 0.85rem !important;
            background: rgba(255, 255, 255, 0.9) !important;
            color: #1f2937 !important;
            font-weight: 500 !important;
            outline: none !important;
        }


        @media (max-width: 768px) {
            #directorySearchInput {
                padding: 0 2.25rem 0 2.25rem !important;
                font-size: 0.8rem !important;
                border-radius: 8px !important;
            }
        }

        @media (max-width: 480px) {
            #directorySearchInput {
                padding: 0 2rem 0 2rem !important;
                font-size: 0.75rem !important;
                border-radius: 6px !important;
            }
        }

        .search-input:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
            background: rgba(255, 255, 255, 1);
        }

        .search-icon {
            position: absolute;
            left: 1rem;
            color: #6b7280;
            font-size: 0.95rem;
            z-index: 2;
            pointer-events: none;
            top: 50%;
            transform: translateY(-50%);
            transition: all 0.3s ease;
        }

        .search-input:focus ~ .search-icon {
            color: #3b82f6;
            transform: translateY(-50%) scale(1.1);
        }

        .clear-btn {
            position: absolute;
            right: 0.8rem;
            background: rgba(239, 68, 68, 0.08);
            border: 1px solid rgba(239, 68, 68, 0.15);
            color: #ef4444;
            cursor: pointer;
            padding: 0.4rem;
            border-radius: 8px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 2;
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            top: 50%;
            transform: translateY(-50%) scale(0.8) rotate(90deg);
            opacity: 0;
            visibility: hidden;
            font-size: 0.8rem;
        }

        .clear-btn.visible {
            opacity: 1;
            visibility: visible;
            transform: translateY(-50%) scale(1) rotate(0deg);
        }

        .clear-btn:hover {
            background: rgba(239, 68, 68, 0.15);
            border-color: rgba(239, 68, 68, 0.3);
            transform: translateY(-50%) scale(1.05) rotate(-90deg);
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.2);
        }

        .clear-btn:active {
            transform: translateY(-50%) scale(0.95) rotate(-90deg);
        }




        .directory-not-found-message {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0.8);
            background: linear-gradient(135deg, #ffffff 0%, #fef2f2 100%);
            border: 2px solid #fecaca;
            border-radius: 16px;
            padding: 1.5rem 2rem;
            box-shadow:
                0 20px 40px rgba(239, 68, 68, 0.15),
                0 8px 16px rgba(0, 0, 0, 0.1);
            z-index: 10000;
            max-width: 500px;
            min-width: 350px;
            opacity: 0;
            visibility: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(10px);
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }

        .directory-not-found-message.show {
            opacity: 1;
            visibility: visible;
            transform: translate(-50%, -50%) scale(1);
        }

        .directory-not-found-message i {
            color: #ef4444;
            font-size: 1.5rem;
            margin-bottom: 1rem;
            display: block;
            text-align: center;
        }

        .directory-not-found-message .message-content {
            text-align: center;
        }

        .directory-not-found-message .message-text {
            color: #374151;
            font-size: 1rem;
            line-height: 1.5;
            margin-bottom: 1rem;
        }

        .directory-not-found-message .message-text strong {
            color: #dc2626;
            font-weight: 700;
        }

        .directory-not-found-message .message-text small {
            color: #6b7280;
            font-size: 0.85rem;
            display: block;
            margin-top: 0.5rem;
        }

        .switch-to-all-btn {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 12px;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .switch-to-all-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
        }

        .switch-to-all-btn:active {
            transform: translateY(0);
        }


        .switch-to-all-btn-map {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 12px;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
            margin-top: 0.5rem;
        }

        .switch-to-all-btn-map:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
        }

        .switch-to-all-btn-map:active {
            transform: translateY(0);
        }



        .department-title i {
            font-size: 0.9rem;
            opacity: 0.9;
        }


        .results-counter,
        #directorySearchResultsCount,
        #mapSearchResultsCount {
            display: none !important;
        }



        .search-results-count {
            color: #6b7280;
            font-size: 0.9rem;
            font-weight: 500;
            min-width: 200px;
            text-align: right;
            flex-shrink: 0;
            background: rgba(59, 130, 246, 0.08);
            padding: 0.5rem 1rem;
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .dark-mode .search-results-count {
            background: rgba(30, 64, 175, 0.2);
            color: #cbd5e1;
        }


        .search-input-wrapper .search-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #6b7280;
            font-size: 1rem;
            z-index: 2;
            pointer-events: none;
        }

        .dark-mode .search-input-wrapper .search-icon {
            color: #9ca3af;
        }

        .search-input-wrapper .search-clear-btn {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #6b7280;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 8px;
            transition: all 0.3s ease;
            z-index: 2;
        }

        .search-input-wrapper .search-clear-btn:hover {
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
        }

        .dark-mode .search-input-wrapper .search-clear-btn {
            color: #9ca3af;
        }

        .dark-mode .search-input-wrapper .search-clear-btn:hover {
            background: rgba(59, 130, 246, 0.2);
            color: #60a5fa;
        }





        .suggestion-details {
            font-size: 0.875rem;
            color: #6b7280;
        }

        .dark-mode .suggestion-details {
            color: #94a3b8;
        }


        .dark-mode .employee-content {
            background: rgba(15, 23, 42, 0.5);
        }

        .dark-mode .filter-header {
            background: rgba(30, 41, 59, 0.8);
            color: #e2e8f0;
        }

        .dark-mode .employee-count {
            color: #cbd5e1;
        }


        .dark-mode .employee-bar {
            background: rgba(15, 23, 42, 0.9);
            border-bottom-color: rgba(59, 130, 246, 0.3);
        }

        .dark-mode .employee-list-horizontal {
            background: rgba(30, 41, 59, 0.8);
        }


        .dark-mode .departments-panel,
        .dark-mode .departments-panel-directory {
            background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
            border-right-color: rgba(59, 130, 246, 0.3);
            box-shadow: 4px 0 20px rgba(0, 0, 0, 0.3);
        }

        .dark-mode .departments-panel .panel-header,
        .dark-mode .departments-panel-directory .panel-header {
            background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%);
            border-bottom-color: rgba(59, 130, 246, 0.4);
        }


        .dark-mode .error-message {
            background: rgba(30, 41, 59, 0.9);
            color: #f87171;
            border-color: rgba(239, 68, 68, 0.3);
        }


        .dark-mode #backToTopBtn {
            background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%);
            box-shadow: 0 4px 15px rgba(30, 64, 175, 0.3);
        }

        .dark-mode #backToTopBtn:hover {
            background: linear-gradient(135deg, #1d4ed8 0%, #1e3a8a 100%);
            box-shadow: 0 8px 25px rgba(30, 64, 175, 0.4);
        }


        .dark-mode .main-search-input::placeholder {
            color: #9ca3af;
        }


        .dark-mode .employee_listing {
            background: transparent;
        }


        .dark-mode #Footer {
            background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
            color: #cbd5e1;
            border-top: 1px solid rgba(59, 130, 246, 0.3);
        }


        .dark-mode .office-map-container {
            background: rgba(15, 23, 42, 0.8);
            border: 1px solid rgba(59, 130, 246, 0.3);
        }


        .dark-mode .employee-list-horizontal li {
            background: rgba(30, 41, 59, 0.8);
            border-color: rgba(59, 130, 246, 0.3);
            color: #e2e8f0;
        }

        .dark-mode .employee-list-horizontal li:hover {
            background: rgba(59, 130, 246, 0.2);
            border-color: #3b82f6;
        }

        .dark-mode .employee-list-horizontal li.active {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            border-color: #3b82f6;
            color: white;
        }


        @media (max-width: 768px) {
            .search-container-simple {
                margin: 1rem 0;
                padding: 1rem;
            }

            .map-search-header {
                margin: 0.5rem;
                padding: 1rem;
                flex-direction: column;
                gap: 0.75rem;
            }

            .employee-bar {
                position: relative !important;
                top: auto !important;
                left: auto !important;
                width: 100% !important;
                margin: 0 0 1rem 0;
                border-radius: 20px;
                border: 1px solid rgba(37,99,235,0.1);
            }

            .employee-bar.panel-collapsed {
                left: auto !important;
                width: 100% !important;
            }

            .main-flex-custom {
                padding-top: 0;
                margin-left: 0 !important;
            }

            .main-flex-custom.panel-collapsed {
                margin-left: 0 !important;
            }

            .mapa-wrapper,
            .mapa-wrapper-custom {
                margin-left: 0 !important;
                margin-top: 0 !important;
            }

            .mapa-wrapper.panel-collapsed,
            .mapa-wrapper-custom.panel-collapsed {
                margin-left: 0 !important;
            }

            .unified-search-container {
                padding: 0.75rem;
                margin-bottom: 0.75rem;
                border-radius: 12px;
            }

            .section-indicator {
                padding: 0.4rem 0.6rem;
                margin-bottom: 0.5rem;
                font-size: 0.85rem;
                border-radius: 8px;
            }

            .section-info i {
                font-size: 0.8rem;
                margin-right: 0.3rem;
            }

            .section-badge {
                font-size: 0.7rem;
                padding: 0.15rem 0.5rem;
            }


            .search-row-inline {
                flex-direction: column;
                gap: 0.75rem;
            }

            .section-indicator-inline {
                padding: 0.3rem 0.6rem;
                font-size: 0.8rem;
                border-radius: 8px;
                min-width: auto;
                max-width: none;
                width: 100%;
                height: 36px;
                line-height: 1.2;
                vertical-align: middle;
            }

            .section-indicator-inline i {
                font-size: 0.75rem;
            }



            .department-title {
                min-width: auto;
                width: 100%;
                justify-content: center;
                margin-bottom: 0.5rem;
            }

            .search-results-count {
                text-align: center;
                margin-top: 0.5rem;
            }
        }

        @media (max-width: 480px) {
            .search-input-simple {
                padding: 0.75rem 2.5rem 0.75rem 2.5rem;
                font-size: 0.9rem;
            }

            .search-input-wrapper .search-icon {
                left: 0.75rem;
            }

            .search-input-wrapper .search-clear-btn {
                right: 0.75rem;
            }
        }


        .employee-content .filter-header {
            display: block !important;
            justify-content: flex-start !important;
            margin-bottom: 0 !important;
        }

        .search-container {
            position: relative;
            flex: 1;
            max-width: 400px;
            flex-shrink: 0;
        }



        .search-box-horizontal {
            width: 100%;
            padding: 1rem 3rem 1rem 1.5rem;
            border: 2px solid rgba(59, 130, 246, 0.2);
            border-radius: 16px;
            font-size: 1rem;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            outline: none;
            color: #1f2937;
            font-weight: 500;
        }

        .search-box-horizontal:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            background: rgba(255, 255, 255, 0.95);
        }

        .search-clear-btn {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #6b7280;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 8px;
            transition: all 0.3s ease;
            opacity: 0;
            pointer-events: none;
        }

        .search-clear-btn.visible {
            opacity: 1;
            pointer-events: auto;
        }

        .search-clear-btn:hover {
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
        }

        .search-results-count {
            color: #6b7280;
            font-size: 0.9rem;
            font-weight: 500;
            min-width: 200px;
            text-align: right;
            background: rgba(37,99,235,0.08);
            border-radius: 8px;
            margin-left: 0.5rem;
            white-space: nowrap;
            transition: all 0.3s ease;
        }

        .search-results-count.visible {
            display: flex;
            align-items: center;
        }


        .search-hint, .not-found-message {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0.8);
            padding: 1rem 1.5rem;
            border-radius: 12px;
            font-weight: 600;
            font-size: 0.95rem;
            z-index: 10001;
            opacity: 0;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: 0.75rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
        }

        .search-hint {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .not-found-message {
            background: linear-gradient(135deg, #ffffff 0%, #fef2f2 100%);
            color: #374151;
            border: 2px solid #fecaca;
            min-width: 350px;
            max-width: 500px;
            padding: 1.5rem 2rem;
            flex-direction: column;
            text-align: center;
            box-shadow: 0 20px 40px rgba(239, 68, 68, 0.15), 0 8px 16px rgba(0, 0, 0, 0.1);
        }

        .search-hint.show, .not-found-message.show {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1);
        }

        .search-hint i, .not-found-message i {
            font-size: 1.5rem;
            opacity: 0.9;
        }


        .not-found-message .message-content {
            text-align: center;
            width: 100%;
        }

        .not-found-message .message-text {
            font-size: 1rem;
            line-height: 1.5;
            margin-bottom: 1rem;
            color: #374151;
        }

        .not-found-message .message-text strong {
            color: #dc2626;
            font-weight: 700;
        }

        .not-found-message .message-text small {
            color: #6b7280;
            font-size: 0.85rem;
            display: block;
            margin-top: 0.5rem;
        }

        .not-found-message i {
            color: #ef4444;
            margin-bottom: 1rem;
            display: block;
        }


        .dark-mode .unified-search-container {
            background: linear-gradient(135deg, rgba(30, 41, 59, 0.95) 0%, rgba(15, 23, 42, 0.95) 100%);
            border-color: rgba(59, 130, 246, 0.3);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
        }

        .dark-mode .section-indicator-inline {
            background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%);
            box-shadow: 0 2px 8px rgba(30, 64, 175, 0.3);
        }

        .dark-mode .search-input {
            background: rgba(30, 41, 59, 0.9);
            border-color: rgba(59, 130, 246, 0.25);
            color: #e2e8f0;
        }

        .dark-mode .search-input:focus {
            background: rgba(30, 41, 59, 1);
            border-color: #3b82f6;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
        }


        .directory-not-found-message {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0.8);
            background: linear-gradient(135deg, #ffffff 0%, #fef2f2 100%);
            color: #374151;
            border: 2px solid #fecaca;
            border-radius: 16px;
            padding: 1.5rem 2rem;
            box-shadow:
                0 20px 40px rgba(239, 68, 68, 0.15),
                0 8px 16px rgba(0, 0, 0, 0.1);
            z-index: 10001;
            max-width: 500px;
            min-width: 350px;
            opacity: 0;
            visibility: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(10px);
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }

        .directory-not-found-message.show {
            opacity: 1;
            visibility: visible;
            transform: translate(-50%, -50%) scale(1);
        }

        .directory-not-found-message i {
            color: #ef4444;
            font-size: 1.5rem;
            margin-bottom: 1rem;
            display: block;
            text-align: center;
        }

        .directory-not-found-message .message-content {
            text-align: center;
            width: 100%;
        }

        .directory-not-found-message .message-text {
            color: #374151;
            font-size: 1rem;
            line-height: 1.5;
            margin-bottom: 1rem;
        }

        .directory-not-found-message .message-text strong {
            color: #dc2626;
            font-weight: 700;
        }

        .directory-not-found-message .message-text small {
            color: #6b7280;
            font-size: 0.85rem;
            display: block;
            margin-top: 0.5rem;
        }

        .directory-not-found-message span {
            flex: 1;
        }

        .dark-mode .directory-not-found-message {
            background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
            color: #e2e8f0;
            border-color: rgba(239, 68, 68, 0.3);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .dark-mode .directory-not-found-message .message-text {
            color: #e2e8f0;
        }

        .dark-mode .directory-not-found-message .message-text strong {
            color: #fca5a5;
        }

        .dark-mode .directory-not-found-message .message-text small {
            color: #94a3b8;
        }

        .dark-mode .directory-not-found-message i {
            color: #fca5a5;
        }

        .dark-mode .not-found-message {
            background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
            color: #e2e8f0;
            border-color: rgba(239, 68, 68, 0.3);
        }

        .dark-mode .not-found-message .message-text {
            color: #e2e8f0;
        }

        .dark-mode .not-found-message .message-text strong {
            color: #fca5a5;
        }

        .dark-mode .not-found-message .message-text small {
            color: #94a3b8;
        }

        .dark-mode .search-icon {
            color: #9ca3af;
        }

        .dark-mode .clear-btn {
            color: #9ca3af;
        }

        .dark-mode .clear-btn:hover {
            background: rgba(59, 130, 246, 0.2);
            color: #60a5fa;
        }



        .dark-mode .department-title {
            background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
            border: 1px solid #475569;
            color: #e2e8f0;
        }




        @media (max-width: 768px) {
            .department-title {
                min-width: 120px;
                font-size: 0.75rem;
                padding: 0.4rem 0.8rem;
            }

            .department-title span {
                display: none;
            }

            .department-title::after {
                content: attr(data-short);
            }

            .unified-search-container {
                padding: 0.4rem 0.6rem;
                border-radius: 10px;
                margin-bottom: 0.5rem;
            }

            .section-indicator {
                padding: 0.35rem 0.5rem;
                font-size: 0.8rem;
                margin-bottom: 0.4rem;
                border-radius: 6px;
            }

            .section-info i {
                font-size: 0.75rem;
                margin-right: 0.25rem;
            }

            .section-badge {
                font-size: 0.65rem;
                padding: 0.1rem 0.4rem;
            }

            .search-input {
                padding: 0 2.25rem 0 2.25rem;
                font-size: 0.8rem;
                height: 36px;
                border-radius: 8px;
                line-height: 1;
            }

            .search-window-inline {
                height: 36px;
            }

            .search-input-wrapper {
                height: 36px;
            }

            .search-icon {
                left: 0.6rem;
                font-size: 0.75rem;
            }

            .clear-btn {
                right: 0.6rem;
                width: 22px;
                height: 22px;
                padding: 0.2rem;
            }

            .results-counter {
                font-size: 0.75rem;
                padding: 0.3rem 0.6rem;
                border-radius: 6px;
                margin-top: 0.2rem;
            }

            /* Tablet inline search adjustments */
            .search-row-inline {
                flex-direction: column;
                gap: 0.6rem;
                height: auto; /* Flexible height on tablet */
            }

            .section-indicator-inline {
                padding: 0 0.5rem;
                font-size: 0.75rem;
                border-radius: 8px;
                min-width: auto;
                max-width: none;
                width: 100%;
                height: 36px; /* Tablet výška */
                line-height: 1;
            }

            .section-indicator-inline i {
                font-size: 0.7rem;
            }
        }

        @media (max-width: 480px) {
            .unified-search-container {
                padding: 0.35rem 0.5rem;
                border-radius: 8px;
                margin-bottom: 0.4rem;
            }

            .search-row-inline {
                flex-direction: column;
                gap: 0.5rem;
                height: auto; /* Flexible height on mobile */
            }

            .section-indicator-inline {
                padding: 0 0.4rem;
                font-size: 0.7rem;
                border-radius: 6px;
                min-width: auto;
                max-width: none;
                width: 100%;
                height: 34px; /* Mobile výška */
                line-height: 1;
            }

            .section-indicator-inline i {
                font-size: 0.65rem;
            }

            .search-input {
                padding: 0 2rem 0 2rem;
                font-size: 0.75rem;
                height: 34px;
                border-radius: 6px;
                line-height: 1;
            }

            .search-window-inline {
                height: 34px; /* Mobile výška */
            }

            .search-input-wrapper {
                height: 34px; /* Mobile výška */
            }

            .search-icon {
                left: 0.5rem;
                font-size: 0.7rem;
            }

            .clear-btn {
                right: 0.5rem;
                width: 20px;
                height: 20px;
                padding: 0.15rem;
            }
        }

        /* Skrytí tlačítka "back to top" ve vnořené mapě */
        body.map-view #backToTopBtn {
            display: none;
        }
    </style>


</head>

<body>

    <header id="headerNav">
        <div class="navbar">
            <div class="logo">
                <a href="index.html"><img src="img/logo1.svg" alt="Logo OTE" class="logo1"></a>
            </div>
            <div class="header-content">
                <h1 class="header-title" id="pageTitle">Adresář OTE</h1>
                <div class="view-toggle-header">
                    <button id="employeeListBtn" class="view-toggle-btn active" data-view="employees" onclick="switchToEmployees()">
                        <i class="fas fa-users"></i>
                        <span>Adresář</span>
                    </button>
                    <button id="officeMapBtn" class="view-toggle-btn" data-view="map" onclick="switchToMap()">
                        <i class="fas fa-map-marked-alt"></i>
                        <span>Mapa</span>
                    </button>
                </div>
            </div>
            <div class="header-actions">
                <button class="theme-toggle-btn" id="themeToggle" title="Přepnout režim">
                    <i class="fas fa-sun" id="themeIcon"></i>
                </button>
            </div>
        </div>
    </header>




    <section class="employee_directory" id="employeeSection">
        <div class="employee-directory-container">

            <div class="departments-panel-directory" id="departmentsPanelDirectory">
                <div class="panel-header">
                    <h3><i class="fas fa-building"></i> Oddělení</h3>
                </div>
                <div class="departments-list" id="departmentsListDirectory">

                </div>
            </div>

            <button class="panel-toggle-btn" id="directoryPanelToggle" title="Sbalit/rozbalit panel">
                <i class="fas fa-chevron-left"></i>
            </button>


            <div class="employee-content">

                <div class="unified-search-container" id="directorySearchContainer">
                    <div class="search-row-inline">
                        <div class="section-indicator-inline">
                            <i class="fas fa-building"></i>
                            <span id="directoryCurrentSection">Všichni zaměstnanci</span>
                        </div>

                        <div class="search-window-inline">
                            <div class="search-input-wrapper">
                                <i class="fas fa-search search-icon"></i>
                                <input type="text" id="directorySearchInput" placeholder="Vyhledat zaměstnance..." autocomplete="off" class="search-input">
                                <button type="button" class="clear-btn" id="directorySearchClearBtn" style="display: none;">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>



                <div class="filter-header">
                </div>

                <div class="employee_listing" id="employee-grid">
                </div>
            </div>
        </div>
    </section>


    <section class="office_map_section" id="mapSection" style="display: none;">
        <div class="main-flex main-flex-custom">
            <div class="employee-bar">

                <div class="unified-search-container" id="mapSearchContainer">
                    <div class="search-row-inline">
                        <div class="section-indicator-inline">
                            <i class="fas fa-building"></i>
                            <span id="mapCurrentSection">Všichni zaměstnanci</span>
                        </div>

                        <div class="search-window-inline">
                            <div class="search-input-wrapper">
                                <i class="fas fa-search search-icon"></i>
                                <input type="text" id="mapSearchInput" placeholder="Vyhledat zaměstnance..." autocomplete="off" class="search-input">
                                <button type="button" class="clear-btn" id="mapSearchClearBtn" style="display: none;">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <ul class="employee-list-horizontal" id="mapEmployeeList"></ul>
            </div>

            <div class="mapa-wrapper mapa-wrapper-custom">

                <div class="departments-panel" id="departmentsPanel">
                    <div class="panel-header">
                        <h3><i class="fas fa-building"></i> Oddělení</h3>
                    </div>
                    <div class="departments-list" id="departmentsList">

                    </div>
                </div>

                <button class="panel-toggle-btn" id="mapPanelToggle" title="Sbalit/rozbalit panel">
                    <i class="fas fa-chevron-left"></i>
                </button>

                <div class="office-map-container" id="mapContainer">
                    <img id="office-map-img" src="img/Greenline.png" alt="Plán kanceláře">
                </div>
            </div>
        </div>
        <div id="map-error" class="error-message">Obrázek plánu kanceláře se nepodařilo načíst. Zkontrolujte cestu k souboru <b>img/Greenline.png</b> nebo kontaktujte správce.</div>
    </section>

    <footer id="Footer">
        <p>Poslední aktualizace: <span id="lastUpdated"></span></p>
    </footer>

    <button id="backToTopBtn" title="Zpět nahoru">
        <i class="fas fa-chevron-up"></i>
        <span>Nahoru</span>
    </button>

    <div id="employeeModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>


            <div class="modal-header-section">
                <img id="modalImage" src="" alt="Employee Image">
                <h2 id="modalName"></h2>
            </div>


            <div class="modal-info-cards">
                <div class="info-card" id="modalPosition">
                    <i class="fas fa-briefcase"></i>
                    <span>Pracovní pozice: Specialista</span>
                </div>
                <div class="info-card" id="modalDepartment">
                    <i class="fas fa-building"></i>
                    <span>Oddělení: Smluvní vztahy a povolenky</span>
                </div>
            </div>


            <div id="modalOffice"></div>


            <div class="modal-contact-section">
                <div class="contact-grid">
                    <div class="contact-item">
                        <i class="fas fa-phone"></i>
                        <span id="modalPhone">Telefon: +420 234 686 370</span>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-mobile-alt"></i>
                        <span id="modalMobile">Mobil: +420 603 568 443</span>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-envelope"></i>
                        <span id="modalEmail">Email: <EMAIL></span>
                    </div>
                </div>


                <div id="modalTeams"></div>
            </div>
        </div>
    </div>

    <script src="js/navbar.js?v=8"></script>
    <script src="js/script.js?v=8"></script>
    <script src="js/back-to-top.js?v=8"></script>
    <script src="js/update-date.js?v=8"></script>
    <script src="js/map-integration.js?v=8"></script>

    <script>
        document.addEventListener('contextmenu', function (e) {
            e.preventDefault();
        });
    </script>

</body>

</html>
