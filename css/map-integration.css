
.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex: 1;
    gap: 2rem;
}

.header-title {
    margin: 0;
    flex: 1;
    text-align: center;
}


.view-toggle-header {
    display: flex;
    background: rgba(37, 99, 235, 0.1);
    border-radius: 12px;
    padding: 4px;
    gap: 2px;
    border: 1px solid rgba(37, 99, 235, 0.2);
}

.view-toggle-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: transparent;
    border: none;
    border-radius: 8px;
    color: #2563eb;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.view-toggle-btn:hover {
    background: rgba(37, 99, 235, 0.1);
    transform: translateY(-1px);
}

.view-toggle-btn.active {
    background: #2563eb;
    color: white;
    box-shadow: 0 2px 8px rgba(37, 99, 235, 0.3);
}


.dark-mode .view-toggle-header {
    background: rgba(59, 130, 246, 0.15);
    border-color: rgba(59, 130, 246, 0.3);
}

.dark-mode .view-toggle-btn {
    color: #3b82f6;
}

.dark-mode .view-toggle-btn:hover {
    background: rgba(59, 130, 246, 0.2);
}

.dark-mode .view-toggle-btn.active {
    background: #3b82f6;
    color: white;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.4);
}


.dark-mode .office_map_section {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
}

.dark-mode .employee-bar {
    background: linear-gradient(135deg, #334155 0%, #475569 100%);
    border-color: #475569;
}

.dark-mode .search-header {
    background: rgba(30, 41, 59, 0.8);
    border-color: rgba(71, 85, 105, 0.3);
}

.dark-mode .search-box-horizontal {
    background: linear-gradient(135deg, rgba(51, 65, 85, 0.95) 0%, rgba(71, 85, 105, 0.95) 100%);
    border-color: rgba(71, 85, 105, 0.4);
    color: #e2e8f0;
}

.dark-mode .search-box-horizontal:focus {
    border-color: #3b82f6;
    background: linear-gradient(135deg, rgba(71, 85, 105, 1) 0%, rgba(100, 116, 139, 1) 100%);
    box-shadow:
        0 8px 32px rgba(59, 130, 246, 0.25),
        0 0 0 3px rgba(59, 130, 246, 0.1);
}

.dark-mode .search-box-horizontal::placeholder {
    color: #94a3b8;
}

.dark-mode .search-clear-btn {
    background: rgba(71, 85, 105, 0.2);
    color: #94a3b8;
}

.dark-mode .search-clear-btn:hover {
    color: #ef4444;
    background: rgba(239, 68, 68, 0.2);
}

.dark-mode .search-results-count {
    background: rgba(59, 130, 246, 0.15);
    color: #94a3b8;
}

.dark-mode .employee-list-horizontal li {
    background: linear-gradient(135deg, #334155 0%, #475569 100%);
    border-color: rgba(71, 85, 105, 0.3);
}

.dark-mode .employee-list-horizontal li:hover {
    background: linear-gradient(135deg, #475569 0%, #64748b 100%);
    border-color: rgba(59, 130, 246, 0.4);
}

.dark-mode .employee-list-horizontal li.active {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: #fff;
}

.dark-mode .employee-list-horizontal .emp-name {
    color: #e2e8f0;
}

.dark-mode .employee-list-horizontal li.active .emp-name {
    color: #fff;
}

.dark-mode .office-map-container {
    background: #334155;
    border-color: #475569;
}

.dark-mode .view-toggle-compact {
    background: rgba(51, 65, 85, 0.95);
    border-color: rgba(59, 130, 246, 0.4);
}

.dark-mode .toggle-header {
    color: #94a3b8;
}

.dark-mode .toggle-btn {
    color: #94a3b8;
}

.dark-mode .toggle-btn:hover {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
}

.dark-mode .toggle-btn.active {
    background: #3b82f6;
    color: white;
}

.view-toggle-btn i {
    font-size: 1rem;
}

.view-toggle-btn span {
    font-size: 0.85rem;
}


.office_map_section {
    background: #f7f9fb;
    padding: 0;
    margin: 0;
    min-height: calc(100vh - 120px);
}

.main-flex {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    justify-content: flex-start;
    width: 100vw;
    max-width: none;
    margin: 2rem 0 2rem 0;
    gap: 48px;
}

.main-flex-custom {
    flex-direction: column;
    gap: 32px;
    align-items: stretch;
}

.employee-bar {
    width: 100%;
    margin: 0 0 1.5rem 0;
    background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
    box-shadow:
        0 8px 24px rgba(37,99,235,0.06),
        0 4px 8px rgba(37,99,235,0.04),
        inset 0 1px 0 rgba(255,255,255,0.8);
    border-radius: 20px;
    border: 1px solid rgba(37,99,235,0.1);
    padding: 1.25rem 1.5rem;
    position: relative;
    z-index: 10;
    backdrop-filter: blur(10px);
    height: auto;
    min-height: 120px;
}

.search-header {
    display: flex;
    align-items: center;
    gap: 3rem;
    padding: 1rem 1.5rem;
    background: rgba(255,255,255,0.7);
    border-radius: 16px;
    border: 1px solid rgba(37,99,235,0.08);
    backdrop-filter: blur(20px);
    height: 80px;
}

.search-section {
    display: flex;
    align-items: center;
    flex-shrink: 0;
    min-width: 220px;
}

.search-row {
    display: flex;
    align-items: center;
    gap: 1rem;
    width: 100%;
}

.search-left {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 1;
}

.search-container {
    position: relative;
    width: 280px;
    flex-shrink: 0;
}

.search-container::before {
    content: '\f002';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6b7280;
    font-size: 0.875rem;
    z-index: 1;
    pointer-events: none;
}

.search-box-horizontal {
    width: 100%;
    border-radius: 10px;
    border: 2px solid rgba(37,99,235,0.12);
    padding: 0.5rem 2.25rem 0.5rem 2rem;
    font-size: 0.85rem;
    background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,250,255,0.95) 100%);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    outline: none;
    color: #1f2937;
    font-weight: 500;
    box-shadow:
        0 4px 12px rgba(37,99,235,0.08),
        inset 0 1px 0 rgba(255,255,255,0.9);
    backdrop-filter: blur(12px);
    height: 36px;
    position: relative;
}

.search-box-horizontal:focus {
    border-color: #2563eb;
    background: linear-gradient(135deg, rgba(255,255,255,1) 0%, rgba(248,250,255,1) 100%);
    box-shadow:
        0 8px 32px rgba(37,99,235,0.15),
        0 0 0 3px rgba(37,99,235,0.1),
        inset 0 1px 0 rgba(255,255,255,1);
    transform: translateY(-1px);
}

.search-clear-btn {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(107,114,128,0.1);
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    opacity: 0;
    pointer-events: none;
    width: 2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.search-clear-btn.visible {
    opacity: 1;
    pointer-events: auto;
}

.search-clear-btn:hover {
    color: #dc2626;
    background: rgba(220,38,38,0.1);
    transform: translateY(-50%) scale(1.1);
}

.search-results-count {
    display: none;
    font-size: 0.75rem;
    color: #6b7280;
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    background: rgba(37,99,235,0.08);
    border-radius: 8px;
    margin-left: 0.5rem;
    white-space: nowrap;
    transition: all 0.3s ease;
}

.search-results-count.visible {
    display: flex;
    align-items: center;
}


.employee-list-horizontal {
    display: flex;
    flex-direction: row;
    gap: 1rem;
    list-style: none;
    margin: 0;
    padding: 0.5rem 0;
    overflow-x: auto;
    overflow-y: hidden;
    scrollbar-width: thin;
    scrollbar-color: rgba(37,99,235,0.4) rgba(37,99,235,0.1);
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    flex: 1;
    min-width: 0;
    position: relative;
    margin-left: 2rem;
}

.employee-list-horizontal::-webkit-scrollbar {
    height: 6px;
}

.employee-list-horizontal::-webkit-scrollbar-track {
    background: rgba(37,99,235,0.08);
    border-radius: 6px;
    margin: 0 1rem;
}

.employee-list-horizontal::-webkit-scrollbar-thumb {
    background: linear-gradient(90deg, rgba(37,99,235,0.4) 0%, rgba(37,99,235,0.6) 100%);
    border-radius: 6px;
    transition: all 0.3s ease;
}

.employee-list-horizontal::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(90deg, rgba(37,99,235,0.6) 0%, rgba(37,99,235,0.8) 100%);
    transform: scaleY(1.2);
}

.employee-list-horizontal li {
    background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
    border-radius: 12px;
    box-shadow:
        0 4px 16px rgba(37,99,235,0.06),
        0 2px 4px rgba(37,99,235,0.04),
        inset 0 1px 0 rgba(255,255,255,0.8);
    border: 1px solid rgba(37,99,235,0.08);
    padding: 0.75rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.625rem;
    position: relative;
    overflow: hidden;
    min-width: 220px;
    max-width: 260px;
    flex-shrink: 0;
    height: 75px;
}

.employee-list-horizontal li:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow:
        0 16px 40px rgba(37,99,235,0.12),
        0 8px 16px rgba(37,99,235,0.08),
        inset 0 1px 0 rgba(255,255,255,0.9);
    border-color: rgba(37,99,235,0.2);
}

.employee-list-horizontal li.active {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    color: #fff;
    transform: translateY(-2px) scale(1.01);
    box-shadow:
        0 12px 32px rgba(37,99,235,0.25),
        0 4px 12px rgba(37,99,235,0.15);
    border-color: #1d4ed8;
}

.employee-list-horizontal .avatar-img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid rgba(37,99,235,0.1);
    background: #fff;
    box-shadow:
        0 4px 12px rgba(0,0,0,0.08),
        0 2px 4px rgba(0,0,0,0.04);
    flex-shrink: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.employee-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.125rem;
    min-width: 0;
    overflow: hidden;
}

.employee-list-horizontal .emp-name {
    font-weight: 700;
    font-size: 0.85rem;
    color: #1f2937;
    letter-spacing: -0.01em;
    line-height: 1.2;
    margin: 0;
    transition: color 0.3s ease;
    white-space: normal;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    word-break: break-word;
    max-width: 120px;
}

.employee-list-horizontal li.active .emp-name {
    color: white;
}

.employee-list-horizontal .find-btn {
    background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
    color: #fff;
    border: none;
    border-radius: 8px;
    padding: 0.375rem 0.625rem;
    font-size: 0.75rem;
    font-weight: 600;
    box-shadow:
        0 3px 8px rgba(37,99,235,0.25),
        0 1px 3px rgba(37,99,235,0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 0.25rem;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    flex-shrink: 0;
    white-space: nowrap;
}

.employee-list-horizontal .find-btn:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow:
        0 8px 20px rgba(37,99,235,0.3),
        0 4px 8px rgba(37,99,235,0.15);
}

.employee-list-horizontal .find-btn:disabled {
    background: #9ca3af;
    cursor: not-allowed;
    opacity: 0.6;
}

.employee-list-horizontal .find-btn:disabled:hover {
    background: #9ca3af;
    transform: none;
    box-shadow: none;
}

.no-map-position {
    font-size: 12px;
    color: #f59e0b;
    margin-left: 5px;
}


.mapa-wrapper {
    flex: 1 1 auto;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    background: transparent;
    min-width: 0;
    margin-left: 280px;
    margin-top: 0;
    transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1;
}

.mapa-wrapper-custom {
    margin-left: 280px;
    margin-top: 0;
    justify-content: center;
    transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1;
}

.mapa-wrapper.panel-collapsed,
.mapa-wrapper-custom.panel-collapsed {
    margin-left: 0px;
}

.office-map-container {
    position: relative;
    display: inline-block;
    background: #fff;
    box-shadow: 0 8px 32px rgba(37,99,235,0.10);
    border-radius: 24px;
    border: 2.5px solid #e3e8f0;
    padding: 0;
    margin: 0;
    max-width: 1920px;
    width: auto;
    z-index: 1;
}

.office-map-container img {
    display: block;
    width: auto;
    max-width: 1920px;
    height: auto;
    -webkit-user-select: none;
    user-select: none;
    pointer-events: auto;
    background: transparent;
    border-radius: 24px;
    border: none;
}


.view-toggle-floating {
    position: absolute;
    left: 1650px;
    top: 30px;
    z-index: 1000;
    pointer-events: auto;
}

.view-toggle-compact {
    display: flex;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 10px;
    padding: 3px;
    gap: 2px;
    border: 1px solid rgba(37, 99, 235, 0.3);
    backdrop-filter: blur(15px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: scale(1.2);
}

.toggle-header {
    text-align: center;
    font-size: 10px;
    font-weight: 600;
    color: #64748b;
    padding: 2px 6px;
    margin-bottom: 2px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.toggle-buttons {
    display: flex;
    gap: 2px;
}

.toggle-btn {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 7px 12px;
    border: none;
    background: transparent;
    border-radius: 7px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 12px;
    font-weight: 500;
    color: #64748b;
    white-space: nowrap;
}

.toggle-btn:hover {
    background: rgba(37, 99, 235, 0.1);
    color: #2563eb;
    transform: translateY(-1px);
}

.toggle-btn.active {
    background: #2563eb;
    color: white;
    box-shadow: 0 2px 6px rgba(37, 99, 235, 0.3);
}

.toggle-btn svg {
    width: 15px;
    height: 15px;
    flex-shrink: 0;
}

.toggle-btn span {
    font-size: 11px;
    font-weight: 600;
}


.marker {
    width: 36px;
    height: 36px;
    background: transparent;
    border: none;
    border-radius: 50%;
    position: absolute;
    cursor: grab;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: none;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    outline: none;
    user-select: none;
    -webkit-user-select: none;
    padding: 0;
}

.marker:active {
    cursor: grabbing;
}

.marker:hover {
    transform: scale(1.1);
    z-index: 10;
}

.marker-avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    border: none;
    box-shadow: none;
    background: none;
    display: block;
}


.marker.female,
.marker.male,
.marker {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.6);
    border: 2px solid #3b82f6;
}

.marker.female:hover,
.marker.male:hover,
.marker:hover {
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.8);
    transform: scale(1.15);
    border-color: #2563eb;
}

.marker.active {
    z-index: 15;
    transform: scale(1.2);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.8) !important;
    border: 2px solid #ef4444 !important;
}

.marker.active:hover {
    box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.9) !important;
    border-color: #dc2626 !important;
}

.marker.spotlight {
    animation: spotlightGlow 3s ease-in-out infinite;
    z-index: 20;
}

@keyframes spotlightGlow {
    0%, 100% {
        transform: scale(1.2);
        box-shadow:
            0 0 0 4px rgba(255, 255, 255, 0.9),
            0 0 0 8px rgba(255, 215, 0, 0.6),
            0 0 20px rgba(255, 215, 0, 0.4),
            0 0 40px rgba(255, 215, 0, 0.2);
    }
    25% {
        transform: scale(1.4);
        box-shadow:
            0 0 0 4px rgba(255, 255, 255, 1),
            0 0 0 12px rgba(255, 215, 0, 0.8),
            0 0 30px rgba(255, 215, 0, 0.6),
            0 0 60px rgba(255, 215, 0, 0.3);
    }
    50% {
        transform: scale(1.6);
        box-shadow:
            0 0 0 4px rgba(255, 255, 255, 1),
            0 0 0 16px rgba(255, 215, 0, 0.4),
            0 0 40px rgba(255, 215, 0, 0.8),
            0 0 80px rgba(255, 215, 0, 0.4);
    }
    75% {
        transform: scale(1.4);
        box-shadow:
            0 0 0 4px rgba(255, 255, 255, 1),
            0 0 0 12px rgba(255, 215, 0, 0.8),
            0 0 30px rgba(255, 215, 0, 0.6),
            0 0 60px rgba(255, 215, 0, 0.3);
    }
}


.tooltip {
    position: absolute;
    top: -44px;
    left: 50%;
    transform: translateX(-50%);
    background: #222;
    color: #fff;
    padding: 7px 16px;
    border-radius: 8px;
    font-size: 16px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s;
    z-index: 10;
    box-shadow: 0 2px 8px rgba(0,0,0,0.12);
    min-width: 120px;
    text-align: center;
}

.marker:hover .tooltip, .marker.active .tooltip {
    opacity: 1;
    pointer-events: auto;
}


.office-map-container.single-view .marker {
    opacity: 0.1;
    pointer-events: none;
    transition: all 0.3s ease;
}

.office-map-container.single-view .marker.active,
.office-map-container.single-view .marker.spotlight {
    opacity: 1;
    pointer-events: all;
}


.error-message {
    color: #c62828;
    background: #fff3f3;
    border: 1px solid #ffcdd2;
    border-radius: 8px;
    padding: 16px;
    text-align: center;
    margin: 2rem auto;
    max-width: 600px;
    font-size: 1.2rem;
    display: none;
}


@media (max-width: 1200px) {
    .main-flex {
        flex-direction: column;
        align-items: stretch;
        gap: 24px;
    }

    .employee-bar {
        border-radius: 18px 18px 0 0;
        margin-bottom: 0;
    }

    .view-toggle-floating {
        left: auto;
        right: 30px;
        top: 30px;
    }
}

@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }

    .view-toggle-header {
        order: -1;
        width: 100%;
        justify-content: center;
    }

    .view-toggle-btn span {
        display: none;
    }

    .main-flex {
        margin: 1rem 0;
        gap: 16px;
    }

    .employee-bar {
        padding: 1rem;
        min-height: auto;
    }

    .search-header {
        flex-direction: column;
        gap: 1rem;
        height: auto;
        padding: 1rem;
    }

    .search-section {
        min-width: auto;
        width: 100%;
    }

    .search-container {
        width: 100%;
    }

    .employee-list-horizontal {
        margin-left: 0;
        gap: 0.5rem;
    }

    .employee-list-horizontal li {
        min-width: 180px;
        max-width: 200px;
        height: 65px;
        padding: 0.5rem;
    }

    .employee-list-horizontal .avatar-img {
        width: 35px;
        height: 35px;
    }

    .employee-list-horizontal .emp-name {
        font-size: 0.8rem;
        max-width: 100px;
    }

    .employee-list-horizontal .find-btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.7rem;
    }

    .view-toggle-floating {
        right: 15px;
        top: 15px;
    }

    .view-toggle-compact {
        transform: scale(1);
    }

    .toggle-btn {
        padding: 5px 8px;
        font-size: 10px;
    }

    .toggle-btn svg {
        width: 12px;
        height: 12px;
    }

    .toggle-btn span {
        font-size: 9px;
    }

    .office-map-container {
        border-radius: 16px;
        border-width: 2px;
    }

    .office-map-container img {
        border-radius: 16px;
    }
}

@media (max-width: 480px) {
    .search-header {
        padding: 0.75rem;
    }

    .search-box-horizontal {
        font-size: 0.8rem;
        padding: 0.4rem 2rem 0.4rem 1.5rem;
        height: 32px;
    }

    .employee-list-horizontal li {
        min-width: 160px;
        max-width: 180px;
        height: 60px;
    }

    .employee-list-horizontal .avatar-img {
        width: 30px;
        height: 30px;
    }

    .employee-list-horizontal .emp-name {
        font-size: 0.75rem;
        max-width: 80px;
    }

    .employee-list-horizontal .find-btn {
        padding: 0.2rem 0.4rem;
        font-size: 0.65rem;
    }

    .marker {
        width: 30px;
        height: 30px;
    }

    .tooltip {
        font-size: 14px;
        padding: 5px 12px;
        top: -40px;
    }
}


.dark-mode .office_map_section {
    background: #1a1a1a;
}

.dark-mode .view-toggle-header {
    background: rgba(37, 99, 235, 0.2);
    border-color: rgba(37, 99, 235, 0.3);
}

.dark-mode .view-toggle-btn {
    color: #60a5fa;
}

.dark-mode .view-toggle-btn:hover {
    background: rgba(37, 99, 235, 0.2);
}

.dark-mode .employee-bar {
    background: linear-gradient(135deg, #2a2a2a 0%, #1f2937 100%);
    border-color: rgba(96, 165, 250, 0.2);
}

.dark-mode .search-header {
    background: rgba(42, 42, 42, 0.8);
    border-color: rgba(96, 165, 250, 0.1);
}

.dark-mode .search-box-horizontal {
    background: linear-gradient(135deg, rgba(42, 42, 42, 0.95) 0%, rgba(31, 41, 55, 0.95) 100%);
    border-color: rgba(96, 165, 250, 0.2);
    color: #f0f0f0;
}

.dark-mode .search-box-horizontal:focus {
    border-color: #60a5fa;
    background: linear-gradient(135deg, rgba(42, 42, 42, 1) 0%, rgba(31, 41, 55, 1) 100%);
    box-shadow:
        0 8px 32px rgba(96, 165, 250, 0.15),
        0 0 0 3px rgba(96, 165, 250, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.dark-mode .employee-list-horizontal li {
    background: linear-gradient(135deg, #2a2a2a 0%, #1f2937 100%);
    border-color: rgba(96, 165, 250, 0.1);
}

.dark-mode .employee-list-horizontal li:hover {
    border-color: rgba(96, 165, 250, 0.3);
}

.dark-mode .employee-list-horizontal li.active {
    background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%);
    color: #1f2937;
}

.dark-mode .employee-list-horizontal .emp-name {
    color: #f0f0f0;
}

.dark-mode .employee-list-horizontal li.active .emp-name {
    color: #1f2937;
}

.dark-mode .office-map-container {
    background: #2a2a2a;
    border-color: rgba(96, 165, 250, 0.2);
}

.dark-mode .view-toggle-compact {
    background: rgba(42, 42, 42, 0.95);
    border-color: rgba(96, 165, 250, 0.3);
}

.dark-mode .toggle-btn {
    color: #9ca3af;
}

.dark-mode .toggle-btn:hover {
    background: rgba(96, 165, 250, 0.1);
    color: #60a5fa;
}

.dark-mode .toggle-btn.active {
    background: #60a5fa;
    color: #1f2937;
}

.dark-mode .error-message {
    background: #2a2a2a;
    border-color: #ef4444;
    color: #fca5a5;
}


.office_map_section {
    padding: 2rem 0;
    background: #f7f9fb;
    min-height: calc(100vh - 120px);
}


.map-search-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto 2rem auto;
    padding: 0 2rem;
    gap: 2rem;
}

.map-search-container {
    position: relative;
    flex: 1;
    max-width: 400px;
}

.map-search-input {
    width: 100%;
    padding: 0.75rem 3rem 0.75rem 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 1rem;
    background: white;
    transition: all 0.3s ease;
    outline: none;
}

.map-search-input:focus {
    border-color: #2563eb;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.map-search-clear {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
    transition: all 0.2s ease;
    opacity: 0;
    pointer-events: none;
}

.map-search-clear.visible {
    opacity: 1;
    pointer-events: auto;
}

.map-search-clear:hover {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}


.map-view-toggle {
    display: flex;
    background: white;
    border-radius: 10px;
    padding: 4px;
    gap: 2px;
    border: 1px solid #e5e7eb;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.map-toggle-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: transparent;
    border: none;
    border-radius: 6px;
    color: #6b7280;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.map-toggle-btn:hover {
    background: #f3f4f6;
    color: #374151;
}

.map-toggle-btn.active {
    background: #2563eb;
    color: white;
    box-shadow: 0 1px 3px rgba(37, 99, 235, 0.3);
}


.map-content {
    display: flex;
    max-width: 1200px;
    margin: 0 auto;
    gap: 2rem;
    padding: 0 2rem;
}


.map-sidebar {
    width: 300px;
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
    height: fit-content;
    max-height: 600px;
    overflow-y: auto;
}

.map-sidebar h3 {
    margin: 0 0 1rem 0;
    color: #2563eb;
    font-size: 1.1rem;
    font-weight: 700;
}

.map-employee-list {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.map-employee-list li {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    border-radius: 8px;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    cursor: pointer;
    transition: all 0.2s ease;
}

.map-employee-list li:hover {
    background: #e0f2fe;
    border-color: #0ea5e9;
    transform: translateY(-1px);
}

.map-employee-list li.active {
    background: #2563eb;
    color: white;
    border-color: #1d4ed8;
}

.map-employee-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #e5e7eb;
    flex-shrink: 0;
}

.map-employee-info {
    flex: 1;
    min-width: 0;
}

.map-employee-name {
    font-weight: 600;
    font-size: 0.9rem;
    color: #1f2937;
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.map-employee-list li.active .map-employee-name {
    color: white;
}

.map-employee-dept {
    font-size: 0.75rem;
    color: #6b7280;
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.map-employee-list li.active .map-employee-dept {
    color: rgba(255, 255, 255, 0.8);
}


.map-container {
    flex: 1;
    background: white;
    border-radius: 12px;
    padding: 1rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
}

.office-map-wrapper {
    position: relative;
    display: inline-block;
    width: 100%;
    max-width: 100%;
    overflow: hidden;
    border-radius: 8px;
}

.office-map-wrapper img {
    width: 100%;
    height: auto;
    display: block;
    border-radius: 8px;
}

#map-markers-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}


.map-marker {
    position: absolute;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    pointer-events: auto;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
}

.map-marker img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.map-marker:hover {
    transform: scale(1.2);
    z-index: 20;
}

.map-marker.active {
    transform: scale(1.3);
    z-index: 25;
}

.map-marker.female img,
.map-marker.male img,
.map-marker img {
    border-color: #3b82f6;
}

.map-marker.spotlight {
    animation: spotlight-pulse 2s ease-in-out infinite;
    z-index: 30;
}

@keyframes spotlight-pulse {
    0%, 100% {
        transform: scale(1.2);
        box-shadow: 0 0 0 0 rgba(255, 215, 0, 0.7);
    }
    50% {
        transform: scale(1.4);
        box-shadow: 0 0 0 10px rgba(255, 215, 0, 0);
    }
}


.map-marker-tooltip {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #1f2937;
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    font-size: 0.8rem;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s ease;
    z-index: 100;
    margin-bottom: 8px;
}

.map-marker-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: #1f2937;
}

.map-marker:hover .map-marker-tooltip {
    opacity: 1;
}


.map-container.single-view .map-marker {
    opacity: 0.2;
    pointer-events: none;
}

.map-container.single-view .map-marker.active,
.map-container.single-view .map-marker.spotlight {
    opacity: 1;
    pointer-events: auto;
}


.error-message {
    text-align: center;
    padding: 2rem;
    color: #dc2626;
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 8px;
    margin: 2rem auto;
    max-width: 600px;
}


@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }

    .view-toggle-header {
        order: -1;
        width: 100%;
        justify-content: center;
    }

    .view-toggle-btn span {
        display: none;
    }

    .map-search-bar {
        flex-direction: column;
        gap: 1rem;
        padding: 0 1rem;
    }

    .map-search-container {
        max-width: none;
    }

    .map-content {
        flex-direction: column;
        padding: 0 1rem;
    }

    .map-sidebar {
        width: 100%;
        max-height: 300px;
        order: 2;
    }

    .map-container {
        order: 1;
    }

    .office_map_section {
        padding: 1rem 0;
    }
}

@media (max-width: 480px) {
    .view-toggle-btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
    }

    .map-search-input {
        padding: 0.6rem 2.5rem 0.6rem 0.8rem;
        font-size: 0.9rem;
    }

    .map-sidebar {
        padding: 1rem;
        max-height: 250px;
    }

    .map-employee-list li {
        padding: 0.6rem;
        gap: 0.6rem;
    }

    .map-employee-avatar {
        width: 35px;
        height: 35px;
    }

    .map-employee-name {
        font-size: 0.85rem;
    }

    .map-employee-dept {
        font-size: 0.7rem;
    }

    .map-marker {
        width: 30px;
        height: 30px;
    }
}


.dark-mode .office_map_section {
    background: #1a1a1a;
}

.dark-mode .view-toggle-header {
    background: rgba(37, 99, 235, 0.2);
    border-color: rgba(37, 99, 235, 0.3);
}

.dark-mode .view-toggle-btn {
    color: #60a5fa;
}

.dark-mode .view-toggle-btn:hover {
    background: rgba(37, 99, 235, 0.2);
}

.dark-mode .map-search-input {
    background: #2a2a2a;
    border-color: #374151;
    color: #f0f0f0;
}

.dark-mode .map-search-input:focus {
    border-color: #60a5fa;
    box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
}

.dark-mode .map-view-toggle {
    background: #2a2a2a;
    border-color: #374151;
}

.dark-mode .map-toggle-btn {
    color: #9ca3af;
}

.dark-mode .map-toggle-btn:hover {
    background: #374151;
    color: #f3f4f6;
}

.dark-mode .map-toggle-btn.active {
    background: #60a5fa;
    color: #1f2937;
}

.dark-mode .map-sidebar,
.dark-mode .map-container {
    background: #2a2a2a;
    border-color: #374151;
}

.dark-mode .map-sidebar h3 {
    color: #60a5fa;
}

.dark-mode .map-employee-list li {
    background: #1f2937;
    border-color: #374151;
}

.dark-mode .map-employee-list li:hover {
    background: #374151;
    border-color: #60a5fa;
}

.dark-mode .map-employee-name {
    color: #f0f0f0;
}

.dark-mode .map-employee-dept {
    color: #9ca3af;
}

.dark-mode .error-message {
    background: #2a2a2a;
    border-color: #ef4444;
    color: #fca5a5;
}




.employee-list-horizontal {
    display: flex;
    flex-direction: row;
    gap: 1rem;
    list-style: none;
    margin: 0;
    padding: 0.5rem 0;
    overflow-x: auto;
    overflow-y: hidden;
    scrollbar-width: thin;
    scrollbar-color: rgba(37,99,235,0.4) rgba(37,99,235,0.1);
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    flex: 1;
    min-width: 0;
    position: relative;
    margin-left: 2rem;
}

.employee-list-horizontal::-webkit-scrollbar {
    height: 6px;
}

.employee-list-horizontal::-webkit-scrollbar-track {
    background: rgba(37,99,235,0.08);
    border-radius: 6px;
    margin: 0 1rem;
}

.employee-list-horizontal::-webkit-scrollbar-thumb {
    background: linear-gradient(90deg, rgba(37,99,235,0.4) 0%, rgba(37,99,235,0.6) 100%);
    border-radius: 6px;
    transition: all 0.3s ease;
}

.employee-list-horizontal::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(90deg, rgba(37,99,235,0.6) 0%, rgba(37,99,235,0.8) 100%);
    transform: scaleY(1.2);
}

.employee-list-horizontal li {
    background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
    border-radius: 12px;
    box-shadow:
        0 4px 16px rgba(37,99,235,0.06),
        0 2px 4px rgba(37,99,235,0.04),
        inset 0 1px 0 rgba(255,255,255,0.8);
    border: 1px solid rgba(37,99,235,0.08);
    padding: 0.75rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.625rem;
    position: relative;
    overflow: hidden;
    min-width: 220px;
    max-width: 260px;
    flex-shrink: 0;
    height: 75px;
}

.employee-list-horizontal li:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow:
        0 16px 40px rgba(37,99,235,0.12),
        0 8px 16px rgba(37,99,235,0.08),
        inset 0 1px 0 rgba(255,255,255,0.9);
    border-color: rgba(37,99,235,0.2);
}

.employee-list-horizontal li.active {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    color: #fff;
    transform: translateY(-2px) scale(1.01);
    box-shadow:
        0 12px 32px rgba(37,99,235,0.25),
        0 4px 12px rgba(37,99,235,0.15);
    border-color: #1d4ed8;
}

.employee-list-horizontal .avatar-img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid rgba(37,99,235,0.1);
    background: #fff;
    box-shadow:
        0 4px 12px rgba(0,0,0,0.08),
        0 2px 4px rgba(0,0,0,0.04);
    flex-shrink: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.employee-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.125rem;
    min-width: 0;
    overflow: hidden;
}

.employee-list-horizontal .emp-name {
    font-weight: 700;
    font-size: 0.85rem;
    color: #1f2937;
    letter-spacing: -0.01em;
    line-height: 1.2;
    margin: 0;
    transition: color 0.3s ease;
    white-space: normal;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    word-break: break-word;
    max-width: 120px;
}

.employee-list-horizontal .find-btn {
    background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
    color: #fff;
    border: none;
    border-radius: 8px;
    padding: 0.375rem 0.625rem;
    font-size: 0.75rem;
    font-weight: 600;
    box-shadow:
        0 3px 8px rgba(37,99,235,0.25),
        0 1px 3px rgba(37,99,235,0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 0.25rem;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    flex-shrink: 0;
    white-space: nowrap;
}

.employee-list-horizontal .find-btn:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow:
        0 8px 20px rgba(37,99,235,0.3),
        0 4px 8px rgba(37,99,235,0.15);
}


.mapa-wrapper {
    flex: 1 1 auto;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    background: transparent;
    min-width: 0;
    margin-left: 0;
    z-index: 1;
}

.mapa-wrapper-custom {
    margin-left: 0;
    justify-content: center;
    z-index: 1;
}

.office-map-container {
    position: relative;
    display: inline-block;
    background: #fff;
    box-shadow: 0 8px 32px rgba(37,99,235,0.10);
    border-radius: 24px;
    border: 2.5px solid #e3e8f0;
    padding: 0;
    margin: 0;
    max-width: 1920px;
    width: auto;
    z-index: 1;
}

.office-map-container img {
    display: block;
    width: auto;
    max-width: 1920px;
    height: auto;
    -webkit-user-select: none;
    user-select: none;
    pointer-events: auto;
    background: transparent;
    border-radius: 24px;
    border: none;
}


.view-toggle-floating {
    position: absolute;
    left: 1650px;
    top: 30px;
    z-index: 1000;
    pointer-events: auto;
}

.view-toggle-compact {
    display: flex;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 10px;
    padding: 3px;
    gap: 2px;
    border: 1px solid rgba(37, 99, 235, 0.3);
    backdrop-filter: blur(15px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: scale(1.2);
}

.toggle-header {
    text-align: center;
    font-size: 10px;
    font-weight: 600;
    color: #64748b;
    padding: 2px 6px;
    margin-bottom: 2px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.toggle-buttons {
    display: flex;
    gap: 2px;
}

.toggle-btn {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 7px 12px;
    border: none;
    background: transparent;
    border-radius: 7px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 12px;
    font-weight: 500;
    color: #64748b;
    white-space: nowrap;
}

.toggle-btn:hover {
    background: rgba(37, 99, 235, 0.1);
    color: #2563eb;
    transform: translateY(-1px);
}

.toggle-btn.active {
    background: #2563eb;
    color: white;
    box-shadow: 0 2px 6px rgba(37, 99, 235, 0.3);
}

.toggle-btn svg {
    width: 15px;
    height: 15px;
    flex-shrink: 0;
}

.toggle-btn span {
    font-size: 11px;
    font-weight: 600;
}


.marker {
    width: 36px;
    height: 36px;
    background: transparent;
    border: none;
    border-radius: 50%;
    position: absolute;
    cursor: grab;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: none;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    outline: none;
    user-select: none;
    -webkit-user-select: none;
    padding: 0;
}

.marker:active {
    cursor: grabbing;
}

.marker:hover {
    transform: scale(1.1);
    z-index: 10;
}

.marker-avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    border: none;
    box-shadow: none;
    background: none;
    display: block;
}



.marker.active {
    z-index: 15;
    transform: scale(1.2);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.8) !important;
    border: 2px solid #ef4444 !important;
}

.marker.active:hover {
    box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.9) !important;
    border-color: #dc2626 !important;
}


.tooltip {
    position: absolute;
    top: -44px;
    left: 50%;
    transform: translateX(-50%);
    background: #222;
    color: #fff;
    padding: 7px 16px;
    border-radius: 8px;
    font-size: 16px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s;
    z-index: 10;
    box-shadow: 0 2px 8px rgba(0,0,0,0.12);
    min-width: 120px;
    text-align: center;
}

.marker:hover .tooltip, .marker.active .tooltip {
    opacity: 1;
    pointer-events: auto;
}


.error-message {
    color: #c62828;
    background: #fff3f3;
    border: 1px solid #ffcdd2;
    border-radius: 8px;
    padding: 16px;
    text-align: center;
    margin: 2rem auto;
    max-width: 600px;
    font-size: 1.2rem;
    display: none;
}


@media (max-width: 1200px) {
    .main-flex {
        flex-direction: column;
        align-items: stretch;
        gap: 24px;
    }

    .employee-bar {
        margin: 0 1rem 1rem 1rem;
        padding: 1rem 1.25rem;
    }

    .search-header {
        gap: 1.5rem;
    }

    .search-section {
        min-width: 200px;
    }

    .search-container {
        width: 180px;
    }

    .employee-list-horizontal li {
        min-width: 200px;
        max-width: 240px;
    }

    .view-toggle-floating {
        left: 20px;
        top: 20px;
    }
}

@media (max-width: 768px) {
    .employee-bar {
        padding: 0.875rem 1rem;
        margin: 0 0.5rem 1rem 0.5rem;
        border-radius: 16px;
    }

    .search-header {
        flex-direction: column;
        gap: 0.75rem;
        height: auto;
        padding: 0.75rem;
    }

    .search-section {
        gap: 0.75rem;
        width: 100%;
        min-width: auto;
    }

    .search-container {
        width: 100%;
        max-width: none;
    }

    .search-box-horizontal {
        font-size: 0.85rem;
        padding: 0.625rem 2.5rem 0.625rem 2.25rem;
        height: 38px;
    }

    .employee-list-horizontal {
        width: 100%;
        margin-left: 0;
    }

    .employee-list-horizontal li {
        min-width: 220px;
        max-width: 260px;
        height: 70px;
        padding: 0.75rem;
        gap: 0.625rem;
    }

    .employee-list-horizontal .avatar-img {
        width: 40px;
        height: 40px;
    }

    .employee-list-horizontal .emp-name {
        font-size: 0.875rem;
    }

    .employee-list-horizontal .find-btn {
        padding: 0.375rem 0.625rem;
        font-size: 0.75rem;
    }

    .office-map-container {
        border-radius: 16px;
        margin: 0 0.5rem;
    }

    .office-map-container img {
        border-radius: 16px;
        max-width: calc(100vw - 1rem);
    }

    .view-toggle-floating {
        left: 10px;
        top: 10px;
        transform: scale(0.9);
    }
}

@media (max-width: 480px) {
    .employee-bar {
        padding: 0.75rem;
        margin: 0 0.25rem 0.75rem 0.25rem;
        border-radius: 14px;
    }

    .search-header {
        padding: 0.625rem;
        gap: 0.5rem;
    }

    .search-section {
        gap: 0.5rem;
        align-items: stretch;
    }

    .search-container {
        width: 100%;
    }

    .search-box-horizontal {
        padding: 0.5rem 2.25rem 0.5rem 2rem;
        font-size: 0.8rem;
        height: 36px;
    }

    .employee-list-horizontal li {
        min-width: 180px;
        max-width: 220px;
        height: 60px;
        padding: 0.625rem;
        gap: 0.5rem;
    }

    .employee-list-horizontal .avatar-img {
        width: 36px;
        height: 36px;
    }

    .employee-list-horizontal .emp-name {
        font-size: 0.8rem;
    }

    .employee-list-horizontal .find-btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.7rem;
        gap: 0.25rem;
    }

    .office-map-container {
        margin: 0 0.25rem;
    }

    .office-map-container img {
        max-width: calc(100vw - 0.5rem);
    }
}


.marker.spotlight {
    animation: spotlightGlow 3s ease-in-out infinite;
    z-index: 20;
}

.marker.bounce-in {
    animation: bounceIn 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.marker.found-pulse {
    z-index: 20;
    animation: markerFoundPulse 2s ease-in-out infinite;
}

.marker.extra-pulse {
    animation: markerExtraPulse 1s ease-in-out 3;
}

#mapEmployeeList li.found-highlight {
    background: linear-gradient(135deg, #00add0 0%, #00808f 100%);
    border-color: #00808f;
    transform: scale(1.02);
    box-shadow: 0 4px 16px rgba(0, 128, 143, 0.25);
    color: #fff;
}

@keyframes spotlightGlow {
    0%, 100% {
        transform: scale(1.2);
        box-shadow:
            0 0 0 4px rgba(255, 255, 255, 0.9),
            0 0 0 8px rgba(255, 215, 0, 0.6),
            0 0 20px rgba(255, 215, 0, 0.4),
            0 0 40px rgba(255, 215, 0, 0.2);
    }
    25% {
        transform: scale(1.4);
        box-shadow:
            0 0 0 4px rgba(255, 255, 255, 1),
            0 0 0 12px rgba(255, 215, 0, 0.8),
            0 0 30px rgba(255, 215, 0, 0.6),
            0 0 60px rgba(255, 215, 0, 0.3);
    }
    50% {
        transform: scale(1.6);
        box-shadow:
            0 0 0 4px rgba(255, 255, 255, 1),
            0 0 0 16px rgba(255, 215, 0, 0.4),
            0 0 40px rgba(255, 215, 0, 0.8),
            0 0 80px rgba(255, 215, 0, 0.4);
    }
    75% {
        transform: scale(1.4);
        box-shadow:
            0 0 0 4px rgba(255, 255, 255, 1),
            0 0 0 12px rgba(255, 215, 0, 0.8),
            0 0 30px rgba(255, 215, 0, 0.6),
            0 0 60px rgba(255, 215, 0, 0.3);
    }
}

@keyframes bounceIn {
    0% {
        transform: scale(0.3) rotate(0deg);
        opacity: 0;
    }
    50% {
        transform: scale(1.05) rotate(180deg);
        opacity: 0.8;
    }
    70% {
        transform: scale(0.9) rotate(270deg);
        opacity: 1;
    }
    100% {
        transform: scale(1) rotate(360deg);
        opacity: 1;
    }
}

@keyframes markerFoundPulse {
    0%, 100% {
        transform: scale(1.2);
    }
    50% {
        transform: scale(1.4);
    }
}

@keyframes markerExtraPulse {
    0%, 100% {
        transform: scale(1.2);
    }
    50% {
        transform: scale(1.6);
    }
}


.office-map-container.single-view .marker {
    opacity: 0.1;
    pointer-events: none;
    transition: all 0.3s ease;
}

.office-map-container.single-view .marker.active,
.office-map-container.single-view .marker.spotlight {
    opacity: 1;
    pointer-events: all;
}


.found-toast {
    position: fixed;
    top: 2rem;
    right: 2rem;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 12px;
    box-shadow:
        0 10px 25px rgba(16, 185, 129, 0.3),
        0 4px 10px rgba(16, 185, 129, 0.2);
    z-index: 10000;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.found-toast.show {
    transform: translateX(0);
    opacity: 1;
}

.toast-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 600;
    font-size: 0.95rem;
}

.toast-icon {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.9);
    animation: toastIconBounce 0.6s ease-out;
}

@keyframes toastIconBounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-4px);
    }
    60% {
        transform: translateY(-2px);
    }
}


@media (max-width: 768px) {
    .found-toast {
        top: 1rem;
        right: 1rem;
        left: 1rem;
        transform: translateY(-100%);
    }

    .found-toast.show {
        transform: translateY(0);
    }
}


.employee_directory {
    display: block;
}

.office_map_section {
    display: none;
}

.main-search-section {
    display: block;
}


body.map-view .main-search-section {
    display: none;
}

body.map-view .employee_directory {
    display: none;
}

body.map-view .office_map_section {
    display: block;
}




.hierarchy-badge {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    padding: 0.4rem 0.8rem;
    border-radius: 16px;
    font-size: 0.75rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.4rem;
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.hierarchy-badge.predstavenstvo {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.hierarchy-badge.vedouci {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.modal-badge {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    padding: 0.4rem 0.8rem;
    border-radius: 16px;
    font-size: 0.75rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.4rem;
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.modal-badge.predstavenstvo {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.modal-badge.vedouci {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}


body.map-view .office-map-container {
    margin-left: 0px;
    width: 100%;
}


body.map-view .employee-bar {
    left: 280px;
    width: calc(100% - 280px);
    z-index: 10;
    position: fixed;
    top: 0;
}

body.map-view .mapa-wrapper,
body.map-view .mapa-wrapper-custom {
    padding-top: 140px;
}


.marker {
    position: absolute !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}


.marker > div {
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    display: block !important;
}

body.map-view .search-header {
    z-index: 15;
    position: relative;
}


@media (max-width: 768px) {
    body.map-view .employee-bar {
        margin-left: 0;
        width: 100%;
        left: 0;
    }

    body.map-view .mapa-wrapper,
    body.map-view .mapa-wrapper-custom {
        padding-top: 120px;
    }

    .departments-panel {
        width: 100%;
        height: auto;
        position: relative;
        top: 0;
    }
}


.dark-mode .departments-panel {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    border-right-color: #475569;
}

.dark-mode .panel-header {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
    border-bottom-color: #334155;
}

.dark-mode .department-item {
    background: #334155;
    border-color: #475569;
    color: #e2e8f0;
}

.dark-mode .department-item:hover {
    border-color: #3b82f6;
    background: #475569;
}

.dark-mode .department-item.active {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
}


body.map-view #backToTopBtn {
    display: none;
}


.search-bar-minimal {
    position: relative;
    max-width: 500px;
    margin: 0 auto 20px auto;
}

.search-input-minimal {
    position: relative;
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 25px;
    padding: 0 20px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
}

.search-input-minimal:focus-within {
    background: rgba(255, 255, 255, 0.12);
    border-color: #4A90E2;
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.3);
    transform: translateY(-1px);
}

.search-input-minimal i {
    color: rgba(255, 255, 255, 0.5);
    margin-right: 12px;
    font-size: 14px;
    transition: color 0.3s ease;
}

.search-input-minimal:focus-within i {
    color: #4A90E2;
}

.search-input-minimal input {
    flex: 1;
    background: transparent;
    border: none;
    color: white;
    font-size: 15px;
    padding: 12px 0;
    outline: none;
    font-weight: 400;
}

.search-input-minimal input::placeholder {
    color: rgba(255, 255, 255, 0.4);
    transition: color 0.3s ease;
}

.search-input-minimal:focus-within input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.clear-btn {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.4);
    cursor: pointer;
    padding: 6px;
    border-radius: 50%;
    transition: all 0.2s ease;
    margin-left: 8px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.clear-btn:hover {
    color: white;
    background: rgba(255, 255, 255, 0.1);
    transform: scale(1.1);
}


.suggestions-dropdown {
    position: absolute;
    top: calc(100% + 8px);
    left: 0;
    right: 0;
    background: rgba(20, 30, 50, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    max-height: 280px;
    overflow-y: auto;
    z-index: 1000;
    backdrop-filter: blur(20px);
    display: none;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.search-suggestion-item {
    padding: 12px 16px;
    cursor: pointer;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
}

.search-suggestion-item:hover,
.search-suggestion-item.highlighted {
    background: rgba(74, 144, 226, 0.15);
    transform: translateX(2px);
}

.search-suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    margin-right: 12px;
    object-fit: cover;
    border: 2px solid rgba(255, 255, 255, 0.1);
}

.suggestion-info {
    flex: 1;
}

.suggestion-name {
    color: white;
    font-weight: 500;
    margin-bottom: 2px;
    font-size: 14px;
}

.suggestion-department {
    color: rgba(255, 255, 255, 0.6);
    font-size: 12px;
}


.results-counter {
    text-align: center;
    color: rgba(255, 255, 255, 0.6);
    font-size: 13px;
    margin-bottom: 15px;
    padding: 6px 12px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    display: inline-block;
    margin-left: 50%;
    transform: translateX(-50%);
    font-weight: 400;
}


.map-search-section {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 20px;
}

.department-badge {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.1);
    padding: 8px 16px;
    border-radius: 20px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    font-weight: 500;
    align-self: flex-start;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.department-badge i {
    color: #4A90E2;
}


.dark-mode .search-input-minimal {
    background: rgba(51, 65, 85, 0.8);
    border-color: rgba(71, 85, 105, 0.5);
}

.dark-mode .search-input-minimal:focus-within {
    background: rgba(71, 85, 105, 0.9);
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

.dark-mode .search-input-minimal i {
    color: rgba(226, 232, 240, 0.5);
}

.dark-mode .search-input-minimal:focus-within i {
    color: #3b82f6;
}

.dark-mode .search-input-minimal input {
    color: #e2e8f0;
}

.dark-mode .search-input-minimal input::placeholder {
    color: rgba(226, 232, 240, 0.4);
}

.dark-mode .clear-btn {
    color: rgba(226, 232, 240, 0.4);
}

.dark-mode .clear-btn:hover {
    color: #e2e8f0;
    background: rgba(226, 232, 240, 0.1);
}

.dark-mode .suggestions-dropdown {
    background: rgba(30, 41, 59, 0.95);
    border-color: rgba(71, 85, 105, 0.3);
}

.dark-mode .results-counter {
    color: rgba(226, 232, 240, 0.6);
    background: rgba(226, 232, 240, 0.05);
}

.dark-mode .department-badge {
    background: rgba(51, 65, 85, 0.8);
    border-color: rgba(71, 85, 105, 0.3);
    color: rgba(226, 232, 240, 0.8);
}


@keyframes spotlightGlow {
    0%, 100% {
        transform: scale(1.2);
        box-shadow:
            0 0 0 4px rgba(255, 255, 255, 0.9),
            0 0 0 8px rgba(0, 173, 208, 0.6),
            0 0 20px rgba(0, 173, 208, 0.4),
            0 0 40px rgba(0, 173, 208, 0.2);
    }
    25% {
        transform: scale(1.4);
        box-shadow:
            0 0 0 4px rgba(255, 255, 255, 1),
            0 0 0 12px rgba(0, 173, 208, 0.8),
            0 0 30px rgba(0, 173, 208, 0.6),
            0 0 60px rgba(0, 173, 208, 0.3);
    }
    50% {
        transform: scale(1.6);
        box-shadow:
            0 0 0 4px rgba(255, 255, 255, 1),
            0 0 0 16px rgba(0, 173, 208, 0.4),
            0 0 40px rgba(0, 173, 208, 0.8),
            0 0 80px rgba(0, 173, 208, 0.4);
    }
    75% {
        transform: scale(1.4);
        box-shadow:
            0 0 0 4px rgba(255, 255, 255, 1),
            0 0 0 12px rgba(0, 173, 208, 0.8),
            0 0 30px rgba(0, 173, 208, 0.6),
            0 0 60px rgba(0, 173, 208, 0.3);
    }
}


@keyframes spotlight-pulse {
    0%, 100% {
        transform: scale(1.2);
        box-shadow: 0 0 0 0 rgba(0, 173, 208, 0.7);
    }
    50% {
        transform: scale(1.4);
        box-shadow: 0 0 0 10px rgba(0, 173, 208, 0);
    }
}
